version: 2.1

orbs:
  python: circleci/python@2.1.1

jobs:
  build-and-tests:
    executor:
      name: python/default
      tag: "3.9.6"
    working_directory: ~/repo
    steps:
      - setup_remote_docker
      - checkout:
          path: ~/repo
      - run:
          name: Upgrade pip
          command: pip install --upgrade pip
      - run:
          name: Disable rich progress bar
          command: export PIP_DISABLE_PIP_VERSION_CHECK=1
      - python/install-packages:
          pkg-manager: pip
      - run:
          name: Create test results directory
          command: mkdir -p test-results
      - run:
          name: Run tests
          command: |
            python -m pytest \
              --maxfail=0 \
              --junitxml=test-results/junit.xml \
              --html=test-results/report.html \
              --self-contained-html \
              --verbose \
              --tb=short
      - run:
          name: Display test summary
          command: |
            echo "Test execution completed. Check test-results/ for detailed reports."
            if [ -f test-results/junit.xml ]; then
              echo "JUnit XML report generated successfully"
            fi
            if [ -f test-results/report.html ]; then
              echo "HTML test report generated successfully"
            fi
          when: always
      - store_test_results:
          path: test-results
      - store_artifacts:
          path: test-results
          destination: test-results

workflows:
  workflow:
    jobs:
      - build-and-tests:
          context: global