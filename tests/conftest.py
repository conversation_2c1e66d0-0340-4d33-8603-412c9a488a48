import os
import sys

import pytest
from pymongo import MongoClient
from testcontainers.mongodb import MongoDbContainer
from wsfcommonlibs.db.connection import DBConnection as DBConnLibs

from db.db_connection import DBConnection
from tests.fixtures import (
    mock_competition,
    mock_tournament,
    mock_wsf_fixture,
    mock_market_1x2,
    mock_competition_config_1x2_db
)

os.environ.setdefault('MOI_SPORTMONKS_API_TOKEN', 'test_token_12345')
os.environ.setdefault('MOI_ENTITY_MAPPER_API', 'http://test-mapper')

# Add the project root to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))


@pytest.fixture(scope="session", autouse=True)
def cleanup_test_environment():
    yield

    # Cleanup test-specific environment variables after tests
    test_env_vars = [
        'MOI_ODDS_SCRIPT_TYPE',
        'MOI_COMPETITION_ID_TO_DOWNLOAD',
        'MOI_MARKET_IDS_TO_DOWNLOAD'
    ]
    for var in test_env_vars:
        if var in os.environ:
            del os.environ[var]


@pytest.fixture(scope="session")
def start_mongodb_container():
    with MongoDbContainer("mongo:8.0") as mongodb:
        # Wait for MongoDB to be ready
        mongodb.get_connection_url()
        yield mongodb


@pytest.fixture
def dbconnection(start_mongodb_container, monkeypatch) -> DBConnection:
    test_db_url = start_mongodb_container.get_connection_url()
    source_client = MongoClient(test_db_url)
    return DBConnection(source_client, source_client)


@pytest.fixture
def db_connection_libs(start_mongodb_container) -> DBConnLibs:
    test_db_url = start_mongodb_container.get_connection_url()
    db_connection_libs = DBConnLibs(test_db_url)
    return db_connection_libs


@pytest.fixture
def setup_data_in_db_with_fixtures(dbconnection, mock_competition, mock_tournament, mock_wsf_fixture, mock_market_1x2,
                                   mock_competition_config_1x2_db):
    soccer_db = dbconnection.source_client["all_leagues"]
    main_db = dbconnection.source_client["main"]

    soccer_db.competitions.insert_one(mock_competition)
    soccer_db.tournaments.insert_one(mock_tournament)
    soccer_db.fixtures.insert_one(mock_wsf_fixture)
    main_db.markets.insert_one(mock_market_1x2)
    soccer_db.competition_configs.insert_one(mock_competition_config_1x2_db)

    yield  # Just perform setup, don't return anything

    cleanup_db(dbconnection)


@pytest.fixture
def setup_data_in_db_without_fixtures(dbconnection, mock_competition, mock_tournament, mock_market_1x2,
                                      mock_competition_config_1x2_db):
    soccer_db = dbconnection.source_client["all_leagues"]
    main_db = dbconnection.source_client["main"]

    soccer_db.competitions.insert_one(mock_competition)
    soccer_db.tournaments.insert_one(mock_tournament)

    main_db.markets.insert_one(mock_market_1x2)
    soccer_db.competition_configs.insert_one(mock_competition_config_1x2_db)

    yield  # Just perform setup, don't return anything

    cleanup_db(dbconnection)


def cleanup_db(dbconnection):
    dbconnection.source_client.drop_database("all_leagues")
    dbconnection.source_client.drop_database("market_odds")
    dbconnection.source_client.drop_database("main")
    dbconnection.source_client.drop_database("sportmonks")
