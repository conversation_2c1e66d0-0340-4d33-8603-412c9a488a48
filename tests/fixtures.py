import json
import os
from datetime import datetime, timedelta
from pathlib import Path

import pytest
from bson import ObjectId

FIXTURE_ID = '507f1f77bcf86cd799439013'
COMPETITION_ID = '507f1f77bcf86cd799439011'
TOURNAMENT_ID = '507f1f77bcf86cd799439012'
WSF_1X2_MARKET_ID = "604bb21f9fe884c7dc2ab1ac"


@pytest.fixture
def mock_competition() -> dict:
    return {
        '_id': ObjectId(COMPETITION_ID),
        'desc': 'Test Competition',
        'active': True,
        'externalIds': {
            'sportmonksIds': ['123']
        }
    }


@pytest.fixture
def mock_tournament(mock_competition: dict) -> dict:
    return {
        '_id': ObjectId(TOURNAMENT_ID),
        'competition': {
            '_id': ObjectId(COMPETITION_ID)
        },
        'active': True,
        'current': True
    }


@pytest.fixture
def mock_wsf_fixture() -> dict:
    return {
        '_id': ObjectId(FIXTURE_ID),
        'active': True,
        'date': datetime.now() + timedelta(days=1),
        'status': "FIXTURE",
        'homeTeam': {'_id': ObjectId('507f1f77bcf86cd799439014'), 'name': 'Home Team'},
        'awayTeam': {'_id': ObjectId('507f1f77bcf86cd799439015'), 'name': 'Away Team'},
        'tournament': {
            '_id': ObjectId(TOURNAMENT_ID),
            'competition': {
                '_id': ObjectId(COMPETITION_ID),
                'name': 'Test Competition'
            }
        },
        'sportmonksFixtureId': '19339026'
    }


@pytest.fixture
def mock_market_1x2() -> dict:
    return {
        "_id": ObjectId(WSF_1X2_MARKET_ID),
        "sportmonksId": "1",
        "desc": "1X2"
    }


@pytest.fixture
def mock_competition_config_1x2_db(mock_competition: dict) -> dict:
    return {
        "_id": ObjectId(),
        "competition": {
            "_id": ObjectId(COMPETITION_ID)
        },
        "marketOdds": {
            "markets": [
                {
                    "marketId": ObjectId(WSF_1X2_MARKET_ID),
                    "provider": "SPORTMONKS",
                    "mandatory": True,
                    "bookmakers": ["2"]
                }
            ]
        }
    }


@pytest.fixture
def mock_sm_fixture_with_1x2_odds(request: pytest.FixtureRequest) -> dict:
    test_dir = os.path.dirname(request.module.__file__)
    data_filename = os.path.join(test_dir, "feeds/sm_fixtures_v3_api_response.json")
    return json.loads(Path(data_filename).read_text())
