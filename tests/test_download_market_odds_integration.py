import os
from datetime import datetime, timedelta
from typing import Generator
from unittest.mock import patch, MagicMock

import pytest
from bson import ObjectId
from wsfcommonlibs.db.connection import DBConnection as DBConnLibs

from db.db_connection import DBConnection
from sportmonks.download_market_odds import DownloadMarketOdds
from sportmonks.slack_alerts.market_odds_alerts import MarketOddsAlerts
from tests.fixtures import (
    mock_sm_fixture_with_1x2_odds,
    WSF_1X2_MARKET_ID, FIXTURE_ID
)


class TestDownloadMarketOddsIntegration:

    @patch.dict(os.environ, {'MOI_ODDS_SCRIPT_TYPE': 'UPCOMING', 'TEST_MODE': 'true'})
    @patch('sportmonks.download_market_odds.sportmonks.api.fixtures.get_fixtures_by_league_and_date_range')
    def test_download_upcoming_1x2_odds(
            self,
            mock_get_fixtures_api: MagicMock,
            setup_data_in_db_with_fixtures: Generator[None, None, None],
            dbconnection: DBConnection,
            db_connection_libs: DBConnLibs,
            mock_sm_fixture_with_1x2_odds: dict
    ):
        # Arrange
        mock_get_fixtures_api.return_value = mock_sm_fixture_with_1x2_odds

        # Act
        DownloadMarketOdds(MarketOddsAlerts(dbconnection, is_test_mode=True), dbconnection,
                           db_connection_libs).download_sportmonks_market_odds()

        # Assert
        market_odds_collection = dbconnection.market_odds_client["market_odds"]["sm_market_odds"]
        stored_odds = list(market_odds_collection.find({}))

        assert len(stored_odds) == 3, f"Expected 3 odds, got {len(stored_odds)}"

        directions = set([odd['direction'] for odd in stored_odds])
        assert len(directions) == 1
        assert directions == {'OVER'}

        for odd in stored_odds:
            assert 'fixture' in odd, "Missing fixture field"
            assert 'marketId' in odd, "Missing marketId field"
            assert 'bookmaker' in odd, "Missing bookmaker field"
            assert 'direction' in odd, "Missing direction field"
            assert 'odds' in odd, "Missing odds field"
            assert 'timestamp' in odd, "Missing timestamp field"

            assert odd['marketId'] == WSF_1X2_MARKET_ID
            assert odd['bookmaker'] == 'bet365'
            assert isinstance(odd['odds'], float)
            assert odd['fixture']['_id'] == ObjectId(FIXTURE_ID)
            assert isinstance(odd['timestamp'], datetime)

        odds_by_line = {odd['line']: odd['odds'] for odd in stored_odds}
        assert odds_by_line['1'] == 2.05
        assert odds_by_line['X'] == 3.20
        assert odds_by_line['2'] == 4.10

    @patch.dict(os.environ, {'MOI_ODDS_SCRIPT_TYPE': 'UPCOMING', 'TEST_MODE': 'true'})
    @patch('sportmonks.download_market_odds.sportmonks.api.fixtures.get_fixtures_by_league_and_date_range')
    def test_when_odds_are_already_present_they_should_be_overwritten(
            self,
            mock_get_fixtures_api: MagicMock,
            setup_data_in_db_with_fixtures: Generator[None, None, None],
            dbconnection: DBConnection,
            db_connection_libs: DBConnLibs,
            mock_sm_fixture_with_1x2_odds: dict
    ):
        # Arrange
        mock_get_fixtures_api.return_value = mock_sm_fixture_with_1x2_odds

        market_odds_collection = dbconnection.market_odds_client["market_odds"]["sm_market_odds"]

        old_odds = {
            'fixture': {'_id': ObjectId('507f1f77bcf86cd799439013')},
            'marketId': WSF_1X2_MARKET_ID,
            'bookmaker': 'Old Bookmaker',
            'direction': '1',
            'odds': 1.50,
            'timestamp': datetime.now() - timedelta(hours=1)
        }
        market_odds_collection.insert_one(old_odds)

        # Act
        DownloadMarketOdds(MarketOddsAlerts(dbconnection, is_test_mode=True), dbconnection,
                           db_connection_libs).download_sportmonks_market_odds()

        # Assert
        stored_odds = list(market_odds_collection.find({}))
        assert len(stored_odds) == 3  # Should have 3 new odds, old one deleted

        old_bookmaker_odds = list(market_odds_collection.find({'bookmaker': 'Old Bookmaker'}))
        assert len(old_bookmaker_odds) == 0, "Old odds should have been deleted"

        for odd in stored_odds:
            assert odd['bookmaker'] == 'bet365'

    @patch.dict(os.environ, {'MOI_ODDS_SCRIPT_TYPE': 'UPCOMING', 'TEST_MODE': 'true'})
    @patch('sportmonks.download_market_odds.sportmonks.api.fixtures.get_fixtures_by_league_and_date_range')
    def test_when_there_is_no_fixture_in_the_upcoming_days_then_it_should_do_nothing(
            self,
            mock_get_fixtures_api: MagicMock,
            setup_data_in_db_without_fixtures: Generator[None, None, None],
            dbconnection: DBConnection,
            db_connection_libs: DBConnLibs,
    ):
        DownloadMarketOdds(MarketOddsAlerts(dbconnection, is_test_mode=True), dbconnection,
                           db_connection_libs).download_sportmonks_market_odds()

        mock_get_fixtures_api.assert_not_called()

        market_odds_collection = dbconnection.market_odds_client["market_odds"]["sm_market_odds"]
        stored_odds = list(market_odds_collection.find({}))
        assert len(stored_odds) == 0


if __name__ == '__main__':
    pytest.main([__file__])
