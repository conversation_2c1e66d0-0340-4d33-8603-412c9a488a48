#!/bin/bash

kubectl create job --from=cronjob/sportmonks-download-odds-upcoming sportmonks-download-odds-cron-manual --dry-run=client -o "json" \
  | kubectl apply -f -

#kubectl create job --from=cronjob/sportmonks-download-odds-historical sportmonks-download-odds-cron-manual --dry-run=client -o "json" \
#| kubectl apply -f -

# afterwards run: kubectl delete job sportmonks-download-odds-cron-manual