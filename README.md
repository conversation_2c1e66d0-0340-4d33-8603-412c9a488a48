# Market Odds Ingestor
This component exposes an internal API and provide cronjobs to request data periodically.


## Configuration

TO run most of the scripts you need to setup the following env vars:
```bash
export MOI_SPORTMONKS_API_TOKEN=<SPORTMONKS_API_TOKEN>

export MOI_DB_URL=mongodb+srv://...

export MOI_ENTITY_MAPPER_API=http://...

export MOI_SCRIPT_NAME=SPORTMONKS_DOWNLOAD
```

In order to run the scripts you need to specify which script you want to run setting the env variable `MOI_SCRIPT_NAME`

## Scripts


### - Download sportmonks leagues

This script downloads the sportmonks leagues of the active competitions and stores them in the db (db: `sportmonks`, collection: `leagues`)  

###### Setup
```bash
export MOI_SCRIPT_NAME=SPORTMONKS_DOWNLOAD_LEAGUES
```


### - Download sportmonks odds

This script downloads all the historical or upcoming odds of the supported leagues from sportmonks.

- Historical (odds from Jul 15 2021 to yesterday)
  To download only a specific timeframe you should change params in `download_market_odds.py`
  ```
  # TODO change these params to download specific historical timeframe
  SEASON_YEARS='2021-2022'
  DATE_HISTORY_START_STR = '2021-07-31'
  DATE_HISTORY_END_STR = '2022-01-31'
  ```
- Upcoming (odds from yesterday to the next 15 days)

```bash
./run_odds_downloader.sh
```

Once the job is completed, remember to delete it from k8s with the following command:

```bash
kc delete job sportmonks-download-odds-cron-manual
```


###### Setup
```bash
export MOI_SCRIPT_NAME=SPORTMONKS_DOWNLOAD_ODDS
```

Additional env vars required for this script:

```bash
export MOI_ODDS_SCRIPT_TYPE=HISTORICAL # [HISTORICAL / UPCOMING]

# you can also specify a different database where to save market_odds
export MOI_DB_URL_MARKET_ODDS=mongodb+srv://..
```



### - Player mapping alert

This script checks if players should be mapped. If so, it sends a slack message


## Deployment

In the current config it runs every day at 8 am
```bash
./deploy_scripts.sh
```
