import logging

from db.db_connection import DBConnection
from sportmonks.api.league import get_sportmonks_leagues, get_sportmonks_season

dbc = DBConnection()


def download_sportmonks_leagues():
    competitions = dbc.competitions.get_active_competitions()
    for competition in competitions:
        try:
            # Downloading sportmonks leagues
            sm_league = get_sportmonks_leagues(competition["sportmonksCompetitionId"])
            logging.info(f"Saving league {sm_league['name']} (ID: {sm_league['id']})")
            dbc.sportmonks.save_sportmonks_league(sm_league)

            # Downloading teams in the current season
            sm_current_season = sm_league['current_season_id']
            logging.info(f"Downloading teams in {sm_league['name']} current season (ID: {sm_current_season})")
            sm_teams = get_sportmonks_season(sm_current_season)
            for sm_team in sm_teams:
                logging.info(f"Saving team {sm_team['name']} (ID: {sm_team['id']})")
                dbc.sportmonks.save_sportmonks_team(sm_team)
        except Exception as e:
            logging.error(f"Error while running download league for competition {competition}: {e}")
