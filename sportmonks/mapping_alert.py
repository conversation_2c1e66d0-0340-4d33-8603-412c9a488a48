import logging
import os

from slack_sdk.webhook import <PERSON>hook<PERSON>lient

from mapper_api import labels

PLAYER_MAPPING_SLACK_WEBHOOK = os.getenv("PLAYER_MAPPING_SLACK_WEBHOOK", None)

if PLAYER_MAPPING_SLACK_WEBHOOK is None:
    raise Exception("env var PLAYER_MAPPING_SLACK_WEBHOOK cannot be null")


def send_alert(text):
    webhook = WebhookClient(PLAYER_MAPPING_SLACK_WEBHOOK)

    response = webhook.send(text=f":soccer: Ready to map some players?!", blocks=[
        {
            "type": "section",
            "text": {
                "text": f":fire: *{text}*",
                "type": "mrkdwn"
            }
        },
        {
            "type": "actions",
            "block_id": "actions1",
            "elements": [
                {
                    "type": "button",
                    "text": {
                        "type": "plain_text",
                        "text": "Open Player Mapping UI :point_right:"
                    },
                    "url": "https://backoffice.wallstreetfootball.io/players"
                }
            ]
        }
    ])
    assert response.status_code == 200
    assert response.body == "ok"


def mapping_alert():
    count = labels.get_labels_count()
    unmapped_players = count['unmapped']
    if not unmapped_players or unmapped_players == 0:
        logging.info("No new players to be mapped")
    elif unmapped_players and unmapped_players == 1:
        logging.info(f"{unmapped_players} new player should be mapped.")
        send_alert(f"{unmapped_players} new player should be mapped")
    elif unmapped_players and unmapped_players > 1:
        logging.info(f"{unmapped_players} new players should be mapped.")
        send_alert(f"{unmapped_players} new players should be mapped")
