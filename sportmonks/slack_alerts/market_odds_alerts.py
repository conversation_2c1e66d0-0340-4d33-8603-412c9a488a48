import logging
from datetime import datetime
from typing import Dict

from slack_sdk.webhook import WebhookClient
import logging
from datetime import datetime, date
from typing import Dict, List, Any, Optional
from db.db_connection import DBConnection

from db.db_connection import DBConnection

MOI_ALERTS_SLACK_WEBHOOK = '*********************************************************************************'

WSF_1X2_MARKET_ID = '604bb21f9fe884c7dc2ab1ac'
DAYS_AHEAD_FOR_ALERT = 3


class MarketOddsAlerts:

    def __init__(self, dbc: DBConnection, is_test_mode: bool):
        self.dbc = dbc
        self.is_test_mode = is_test_mode

    def send_alert(self, title: str, text: str) -> None:
        if self.is_test_mode:
            logging.error(f"alert not sent because of test mode: {title} - {text}")
            return
        webhook = WebhookClient(MOI_ALERTS_SLACK_WEBHOOK)
        response = webhook.send(
            text=title,
            blocks=[
                {"type": "section",
                 "text": {"type": "mrkdwn",
                          "text": f"*{title}* - {text}"
                          }
                 }
            ]
        )
        assert response.status_code == 200
        assert response.body == "ok"

    def need_to_send_alert(self, fixture_id: str, market_id: str, fixture_date: datetime) -> bool:
        now = datetime.date(datetime.today())
        days_ahead = (fixture_date.date() - now).days

        is_1x2_market = market_id == WSF_1X2_MARKET_ID
        is_within_alert_days = 0 <= days_ahead <= DAYS_AHEAD_FOR_ALERT

        def check_if_odds_exists() -> bool:
            return self.dbc.market_odds.do_odds_exist(fixture_id, market_id)

        return is_1x2_market and is_within_alert_days and not check_if_odds_exists()

    def alert_if_markets_not_found(
            self, fixture: dict,
            markets_download_status: dict,
            sm_market_id_to_market_info: Dict[str, dict]
    ) -> None:
        mandatory_sm_market_ids = [sm_id for sm_id, market_info in sm_market_id_to_market_info.items() if
                                   market_info["mandatory"]]
        for sm_market_id in mandatory_sm_market_ids:
            if not markets_download_status[sm_market_id]:
                fixture_id = str(fixture["_id"])
                match_str = f"{fixture['homeTeam']['name']} - {fixture['awayTeam']['name']} - {fixture['date']}"
                competition_name = fixture["tournament"]["competition"]["name"]
                log_text = f"No odds for market {sm_market_id_to_market_info[sm_market_id]['market']['desc']} and fixture {fixture_id} {match_str} - {competition_name}"
                logging.warning(log_text)

                wsf_market_id = str(sm_market_id_to_market_info[sm_market_id]['market']['_id'])
                fixture_date = fixture["date"]
                if self.need_to_send_alert(fixture_id, wsf_market_id, fixture_date):
                    self.send_alert(title="Missing Odds Warning", text=log_text)

    def alert_no_odds_found(
            self, fixture_id: str,
            match_str: str,
            competition_name: str,
            fixture_date: datetime
    ):
        log_text = f"No odds found for fixture {fixture_id} {match_str} - {competition_name}"
        logging.warning(log_text)
        if self.need_to_send_alert(fixture_id, WSF_1X2_MARKET_ID, fixture_date):
            self.send_alert(title="Missing Odds Warning", text=log_text)

    def alert_fixture_not_found(self, wsf_fixture: dict):
        fixture_id = str(wsf_fixture["_id"])
        fixture_date = wsf_fixture['date']
        log_text = f"No fixture found in SM feed for {fixture_id}: {wsf_fixture['homeTeam']['name']} - {wsf_fixture['awayTeam']['name']} - {fixture_date}"
        logging.warning(log_text)
        if self.need_to_send_alert(fixture_id, WSF_1X2_MARKET_ID, fixture_date):
            self.send_alert(title=f"Could not find fixtureId={fixture_id} in Sportmonks feed", text=log_text)

    def alert_fixture_error(self, wsf_fixture: dict, error: str):
        fixture_id = str(wsf_fixture["_id"])
        self.send_alert(
            title=f"Error while processing fixtureId={fixture_id}",
            text=f"Error processing fixture={wsf_fixture}: {error}"
        )
