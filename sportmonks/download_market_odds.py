import logging
import os
import traceback
from datetime import datetime, timedelta, date
from itertools import groupby
from typing import Dict, List

from wsfcommonlibs.db.connection import DBConnection as DBConnLibs

import sportmonks.api.fixtures
from db.db_connection import DBConnection
from sportmonks.market_odds_handlers.market_1x2 import parse_1x2_market_odds
from sportmonks.market_odds_handlers.market_exact_goals_number import parse_exact_goals_number_market_odds
from sportmonks.models import BookmakerOdds, MarketOdds
from sportmonks.slack_alerts.market_odds_alerts import MarketOddsAlerts
from sportmonks.sm_fixtures_helpers import SportmonksFixturesHelpers

MOI_MARKET_IDS_TO_DOWNLOAD = os.getenv("MOI_MARKET_IDS_TO_DOWNLOAD", None)

if MOI_MARKET_IDS_TO_DOWNLOAD:
    MOI_MARKET_IDS_TO_DOWNLOAD = MOI_MARKET_IDS_TO_DOWNLOAD.split(',')

MOI_COMPETITION_ID_TO_DOWNLOAD = os.getenv("MOI_COMPETITION_ID_TO_DOWNLOAD", None)

logging.info(f"Markets: {MOI_MARKET_IDS_TO_DOWNLOAD}")

## Default dates for upcoming and historical scripts
# TODO change these params to download specific historical timeframe
HISTORICAL_SEASONS = {
    "2021": {
        "start": datetime.date(datetime.strptime('2021-05-28', '%Y-%m-%d')),
        "end": datetime.date(datetime.strptime('2021-11-28', '%Y-%m-%d'))
    },
    "2022": {
        "start": datetime.date(datetime.strptime('2022-04-09', '%Y-%m-%d')),
        "end": datetime.date(datetime.strptime('2022-11-06', '%Y-%m-%d'))
    },
    "2023": {
        "start": datetime.date(datetime.strptime('2023-04-15', '%Y-%m-%d')),
        "end": datetime.date(datetime.today())
    }
}

DATE_UPCOMING_START = datetime.date(datetime.today())  # today
DATE_UPCOMING_END = datetime.date(datetime.today() + timedelta(days=15))  # 15 days from today

WSF_1X2_MARKET_ID = '604bb21f9fe884c7dc2ab1ac'
WSF_TEAM_GOALS_MARKETS = ["634eac676a769ac8ee692667", "634ea8ee04a9bdf0239127f0"]
LINES_TEAM_GOALS1 = {'0', '1', '2', '3+'}
LINES_TEAM_GOALS2 = {'0', '1', '2', '3', '4+'}
DAYS_AHEAD_FOR_ALERT = 3


class DownloadMarketOdds:
    def __init__(self, market_odds_alerts: MarketOddsAlerts, db_connection: DBConnection,
                 db_connection_libs: DBConnLibs):
        self.market_odds_alerts = market_odds_alerts
        self.db_connection = db_connection
        self.db_connection_libs = db_connection_libs

    def download_sportmonks_market_odds(self):
        script_type = os.getenv("MOI_ODDS_SCRIPT_TYPE", None)
        if script_type is None or (script_type.upper() not in ['HISTORICAL', 'UPCOMING']):
            raise Exception("Invalid env var: MOI_ODDS_SCRIPT_TYPE")

        competitions = self.get_competitions_to_process()
        for competition in competitions:
            try:
                logging.info(f"----- Start Competition {competition['desc']} -----")
                sm_market_id_to_market_info = self.retrieve_markets_for_competition(str(competition["_id"]))
                logging.info("Markets to download: {}".format(sm_market_id_to_market_info))
                if script_type.upper() == 'HISTORICAL':
                    for season, dates in HISTORICAL_SEASONS.items():
                        tournament = self.db_connection.competitions.get_tournament_for_years(competition["_id"],
                                                                                              season)
                        if tournament:
                            self.download_range(dates["start"], dates["end"], competition, sm_market_id_to_market_info)
                elif script_type.upper() == 'UPCOMING':
                    tournament = self.db_connection.competitions.get_current_tournament(competition["_id"])
                    if tournament:
                        self.download_range(DATE_UPCOMING_START, DATE_UPCOMING_END, competition,
                                            sm_market_id_to_market_info)
            except Exception as e:
                log_text = f"Error while running download market odds for competition {competition}: {e}"
                logging.error(log_text)
                traceback.print_exc()
                self.market_odds_alerts.send_alert(title="Running Error", text=log_text)

    def download_range(self, start_date: date, end_date: date, competition: dict,
                       sm_market_id_to_market_info: Dict[str, dict]) -> None:
        days_ahead = (end_date - start_date).days
        competition_name = competition['desc']
        logging.info(f"Downloading market odds for {competition_name} for the next {days_ahead} days")

        wsf_fixtures = self.db_connection.competitions.get_fixtures_by_competition_next_days(str(competition["_id"]),
                                                                                             days_ahead)
        if not wsf_fixtures:
            logging.info(f"No fixtures found for {competition_name} in the next {days_ahead} days")
            return

        sm_competition_ids = competition['externalIds']['sportmonksIds']
        sm_fixtures: List[dict] = self.retrieve_odds_for_date_range(start_date, end_date, sm_competition_ids)

        sm_fixtures_helper = SportmonksFixturesHelpers(self.db_connection, sm_fixtures)
        for wsf_fixture in wsf_fixtures:

            logging.info(
                f"Processing fixture {wsf_fixture['_id']} - {wsf_fixture['homeTeam']['name']} vs {wsf_fixture['awayTeam']['name']} - {wsf_fixture['date']}")

            if not wsf_fixture['active']:
                logging.info(f"Skipping market odds parsing for fixture {wsf_fixture['_id']} as it is not active")
                continue

            sm_fixture = None
            sportmonks_fixture_id = wsf_fixture.get('sportmonksFixtureId')
            if sportmonks_fixture_id:
                sm_fixture = sm_fixtures_helper.find_wsf_fixture_in_sm_fixtures_by_sportmonks_id(sportmonks_fixture_id)

            if sm_fixture is None:
                sm_fixture = sm_fixtures_helper.find_wsf_fixture_in_sm_fixtures_by_teams_and_date(wsf_fixture)

            if sm_fixture is None:
                self.market_odds_alerts.alert_fixture_not_found(wsf_fixture)
                continue

            try:
                self.parse_fixture_odds(sm_fixture, wsf_fixture, sm_market_id_to_market_info)
            except Exception as e:
                logging.error(f"Error processing fixture {wsf_fixture['_id']}: {str(e)}")
                traceback.print_exc()
                self.market_odds_alerts.alert_fixture_error(wsf_fixture, str(e))

    @staticmethod
    def retrieve_odds_for_date_range(start_date: date, end_date: date, sm_competition_ids: List[str]) -> List[dict]:
        sm_fixtures = []
        for sm_competition_id in sm_competition_ids:
            sm_fixtures.extend(sportmonks.api.fixtures.get_fixtures_by_league_and_date_range(
                sm_competition_id,
                start_date.strftime('%Y-%m-%d'),
                end_date.strftime('%Y-%m-%d')
            )['data'])

        return sm_fixtures

    def parse_fixture_odds(self, sm_fixture: dict, fixture: dict, sm_market_id_to_market_info: Dict[str, dict]) -> None:
        match_date = sm_fixture["starting_at"]
        match_str = f"{sm_fixture['name']} - {match_date}"
        fixture_id = str(fixture['_id'])
        competition_name = fixture['tournament']['competition']['name']
        fixture_date = fixture['date']

        if sm_fixture.get("odds") is None:
            self.market_odds_alerts.alert_no_odds_found(fixture_id, match_str, competition_name, fixture_date)
            return

        sm_fixture_odds_list = sm_fixture['odds']

        markets_download_status = {sm_id: False for sm_id, _ in sm_market_id_to_market_info.items()}
        odds_by_market: Dict[str, MarketOdds] = self.group_odds_by_market_id(sm_fixture_odds_list)
        for sm_odds_market_id, odds_for_market in odds_by_market.items():
            if sm_odds_market_id not in sm_market_id_to_market_info:
                continue

            market_data = sm_market_id_to_market_info[sm_odds_market_id]
            market = market_data['market']
            supported_bookmaker_list = market_data['bookmakers']

            odds_by_bookmaker = groupby(sorted(odds_for_market.bookmaker_odds, key=lambda x: x.bookmaker_id),
                                        key=lambda x: x.bookmaker_id)

            market_odds = []
            for bookmaker_id, bookmaker_odds in odds_by_bookmaker:
                if bookmaker_id not in supported_bookmaker_list:
                    continue

                logging.info(f"> Start {market['desc']} from Bookmaker {bookmaker_id}")

                odds = self.parse_odds_by_market(fixture, list(bookmaker_odds), market)
                if self.odds_are_valid(odds, market):
                    market_odds.extend(odds)
                    markets_download_status[sm_odds_market_id] = True
                    break  # Stop after finding valid odds from a supported bookmaker

            market_id = str(market["_id"])
            if len(market_odds) > 0 and market_id:
                self.db_connection.market_odds.delete_sm_market_odds(fixture_id, market_id)
                self.db_connection.market_odds.bulk_insert_market_odds(market_odds)

        self.market_odds_alerts.alert_if_markets_not_found(fixture, markets_download_status,
                                                           sm_market_id_to_market_info)

    @staticmethod
    def group_odds_by_market_id(sm_fixture_odds_list: List[dict]) -> Dict[str, MarketOdds]:
        odds_by_market = {}
        market_odds = []
        for sm_odds in sm_fixture_odds_list:
            bookmaker = sm_odds['bookmaker']
            bookmaker_id = str(bookmaker['id'])

            mapped_odds = BookmakerOdds(bookmaker_id, bookmaker['name'], sm_odds['label'], sm_odds['value'])

            market_odds.append(mapped_odds)

            market_id = str(sm_odds['market_id'])
            odds_by_market[market_id] = MarketOdds(market_id, market_odds)

        return odds_by_market

    def parse_odds_by_market(self, fixture: dict, sm_odds_list: List[BookmakerOdds], wsf_market: dict) -> List[dict]:
        wsf_market_id = str(wsf_market["_id"])

        odds = []
        for sm_odds in sm_odds_list:
            odd = None
            if wsf_market_id == "604bb21f9fe884c7dc2ab1ac":  # 1x2
                odd = parse_1x2_market_odds(wsf_market_id, fixture, sm_odds)
            # TODO Under/Over parsing is not yet supported as SM structure is not clear
            # elif wsf_market_id == "604bb21f9fe884c7dc2ab1af":  # under/over
            #     odd = parse_under_over_market_odds(wsf_market_id, fixture, sm_odds, bookmaker_name)
            # elif wsf_market_id == "604bb21f9fe884c7dc2ab1a4":  # goalscorer
            #     odd = parse_goalscorer_market_odds(self.db_connection, wsf_market_id, fixture, sm_odds, bookmaker_name)
            # elif wsf_market_id == "604bb21f9fe884c7dc2ab1a3":  # assist
            #     odd = parse_assist_market_odds(self.db_connection, wsf_market_id, fixture, sm_odds, bookmaker_name)
            # elif wsf_market_id == "604bb21f9fe884c7dc2ab1a0":  # shots
            #     odd = parse_shots_market_odds(self.db_connection, wsf_market_id, fixture, sm_odds, bookmaker_name)
            # elif wsf_market_id == "604bb21f9fe884c7dc2ab1a5":  # shots on goal
            #     odd = parse_shots_on_goal_market_odds(self.db_connection, wsf_market_id, fixture, sm_odds,
            #                                           bookmaker_name)
            # elif wsf_market_id == "604bb21f9fe884c7dc2ab1a1":  # passes
            #     odd = parse_passes_market_odds(self.db_connection, wsf_market_id, fixture, sm_odds, bookmaker_name)
            # elif wsf_market_id == "604bb21f9fe884c7dc2ab1aa":  # yellow cards
            #     odd = parse_yellow_cards_market_odds(self.db_connection, wsf_market_id, fixture, sm_odds,
            #                                          bookmaker_name)
            elif wsf_market_id == "634eac676a769ac8ee692667":  # home team goals number
                odd = parse_exact_goals_number_market_odds(wsf_market_id, fixture, sm_odds, True,
                                                           self.market_odds_alerts)
            elif wsf_market_id == "634ea8ee04a9bdf0239127f0":  # away team goals number
                odd = parse_exact_goals_number_market_odds(wsf_market_id, fixture, sm_odds, False,
                                                           self.market_odds_alerts)
            else:
                logging.error(f"MISSING ODDS PARSER FOR MARKET ID {wsf_market_id}")

            if odd:
                odds.append(odd)

        return odds

    @staticmethod
    def odds_are_valid(market_odds: List[dict], wsf_market: dict) -> bool:
        wsf_market_id = str(wsf_market["_id"])

        if wsf_market_id in WSF_TEAM_GOALS_MARKETS:
            downloaded_lines = set([odds['line'] for odds in market_odds])
            if downloaded_lines != LINES_TEAM_GOALS1 and downloaded_lines != LINES_TEAM_GOALS2:
                return False

        if wsf_market_id == WSF_1X2_MARKET_ID and len(market_odds) != 3:
            return False

        return True

    def retrieve_markets_for_competition(self, competition_id: str) -> Dict[str, dict]:
        config = self.db_connection_libs.competitions.get_competition_config(competition_id)

        sm_market_id_to_market_info = {}
        for market_config in config["marketOdds"]["markets"]:
            market_id = market_config["marketId"]
            if market_config["provider"] == "SPORTMONKS" and \
                    (not MOI_MARKET_IDS_TO_DOWNLOAD or market_id in MOI_MARKET_IDS_TO_DOWNLOAD):
                market = self.db_connection_libs.markets.get_market_by_id(market_id)
                sm_market_id_to_market_info[market["sportmonksId"]] = {
                    "market": market,
                    "mandatory": market_config["mandatory"],
                    "bookmakers": market_config["bookmakers"]
                }

        return sm_market_id_to_market_info

    def get_competitions_to_process(self) -> List[dict]:
        if MOI_COMPETITION_ID_TO_DOWNLOAD:
            return self.db_connection.competitions.get_competition_by_id(MOI_COMPETITION_ID_TO_DOWNLOAD)
        else:
            return self.db_connection.competitions.get_active_competitions()
