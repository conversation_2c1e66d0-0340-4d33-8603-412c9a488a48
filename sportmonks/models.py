from typing import List


class BookmakerOdds:
    def __init__(self, bookmaker_id: str, bookmaker_name: str, label: str, odds: str):
        self.bookmaker_id = bookmaker_id
        self.bookmaker_name = bookmaker_name
        self.label = label
        self.odds = odds


class MarketOdds:
    def __init__(self, market_id: str, bookmaker_odds: List[BookmakerOdds]):
        self.market_id = market_id
        self.bookmaker_odds = bookmaker_odds
