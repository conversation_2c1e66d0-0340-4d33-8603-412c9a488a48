import logging
from datetime import datetime
from typing import List, Optional

from wsfcommonlibs.db.connection import DBConnection

from sportmonks.market_odds_handlers import commons


class SportmonksFixturesHelpers:

    def __init__(self, db_connection: DBConnection, sm_fixtures: List[dict]):
        self.db_connection = db_connection
        self.sm_fixtures = sm_fixtures

    def find_wsf_fixture_in_sm_fixtures_by_sportmonks_id(self, sportmonks_fixture_id: str):
        return next((sm_fixture for sm_fixture in self.sm_fixtures if str(sm_fixture['id']) == sportmonks_fixture_id), None)

    def find_wsf_fixture_in_sm_fixtures_by_teams_and_date( self, wsf_fixture: dict) -> Optional[dict]:
        fixture_date = wsf_fixture['date']

        sm_fixture = None
        for potential_match in self.sm_fixtures:
            if len(potential_match['participants']) != 2:
                logging.warning(f"Skipping match {potential_match['name']} with more than 2 participants")
                continue

            home_team = next((participant for participant in potential_match['participants']
                              if participant['meta']['location'] == 'home' and not participant['placeholder']), None)
            away_team = next((participant for participant in potential_match['participants']
                              if participant['meta']['location'] == 'away' and not participant['placeholder']), None)

            if not home_team or not away_team:
                logging.warning(f"Skipping match {potential_match['name']} with no home or away team")
                continue

            home_team_id = home_team['id']
            home_team_name = home_team['name']
            away_team_id = away_team['id']
            away_team_name = away_team['name']

            sm_home_team = commons.retrieve_team(self.db_connection, home_team_id, home_team_name)
            sm_away_team = commons.retrieve_team(self.db_connection, away_team_id, away_team_name)

            home_team = wsf_fixture['homeTeam']
            away_team = wsf_fixture['awayTeam']

            logging.debug(f"Looking for SM fixture with homeTeam={home_team_id},{home_team_name} and awayTeam={away_team_id},{away_team_name} and date {fixture_date} in the DB")

            if (sm_home_team and sm_away_team and
                    str(sm_home_team['_id']) == str(home_team['_id']) and
                    str(sm_away_team['_id']) == str(away_team['_id'])):

                # Check if dates are within 1 day of each other
                sm_date = datetime.strptime(potential_match['starting_at'], '%Y-%m-%d %H:%M:%S')
                if abs((sm_date - fixture_date).days) <= 1:
                    sm_fixture = potential_match
                    break
        return sm_fixture
