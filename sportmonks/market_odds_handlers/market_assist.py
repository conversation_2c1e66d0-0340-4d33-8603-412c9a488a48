from datetime import datetime

from sportmonks.market_odds_handlers.commons import retrieve_player, print_parsed_odds_message


def parse_assist_market_odds(dbc, wsf_market_id, fixture, sm_odds, bookmaker_name):
    sm_player_name = sm_odds['extra']
    wsf_player = retrieve_player(dbc, sm_player_name)

    if wsf_player:
        odd = dict(
            fixture=fixture,
            player=wsf_player,
            marketId=wsf_market_id,
            bookmaker=bookmaker_name,
            direction=sm_odds["label"].upper(),
            line=sm_odds["handicap"],
            odds=float(sm_odds['value']),
            timestamp=datetime.now()
        )

        print_parsed_odds_message(wsf_player, odd, "Assist")
        return odd
