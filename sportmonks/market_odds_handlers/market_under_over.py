from datetime import datetime
import logging


# TODO Under/Over parsing is not yet supported as SM structure is not clear
def parse_under_over_market_odds(wsf_market_id, fixture, sm_odds, bookmaker_name):
    direction = sm_odds['label'].upper()
    odd = dict(
        fixture=fixture,
        player=None,
        marketId=wsf_market_id,
        bookmaker=bookmaker_name,
        direction=direction,
        line=sm_odds['total'],
        odds=float(sm_odds['value']),
        timestamp=datetime.now()
    )

    logging.info(f"Market Under/Over: {direction} {odd['line']}: {odd['odds']}")

    return odd
