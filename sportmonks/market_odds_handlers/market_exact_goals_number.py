import logging
import re
from datetime import datetime

from sportmonks.models import BookmakerOdds
from sportmonks.slack_alerts.market_odds_alerts import MarketOddsAlerts


# Market IDs must be updated in the DB to 18,19

def parse_exact_goals_number_market_odds(wsf_market_id: str, fixture: dict, sm_odds: BookmakerOdds, is_home: bool,
                                         market_odds_alerts: MarketOddsAlerts):
    label = sm_odds.label

    # v3 API format patterns:
    # Simple format: "0", "1", "2", "3", "more 2", "more 4"
    # Descriptive format: "Team Name - 0 Goals", "Team Name - 1 Goal", "Team Name - 2 Goals", "Team Name - 3+ Goals"

    if re.match(r'^\d+$', label):
        # Simple number: "0", "1", "2", "3"
        line = label
    elif re.match(r'^more \d+$', label):
        # "more X" format: "more 2", "more 4"
        number = re.findall(r'\d+', label)[0]
        line = number + '+'
    elif re.search(r'- \d+ Goals?$', label):
        # Descriptive format: "Team Name - 0 Goals", "Team Name - 1 Goal", "Team Name - 2 Goals"
        number = re.findall(r'- (\d+) Goals?$', label)[0]
        line = number
    elif re.search(r'- \d+\+ Goals?$', label):
        # Descriptive format with plus: "Team Name - 3+ Goals"
        number = re.findall(r'- (\d+)\+ Goals?$', label)[0]
        line = number + '+'
    else:
        log_text = f"Found unknown line pattern for market {wsf_market_id} fixture {fixture['_id']}: {label}"
        logging.error(log_text)
        market_odds_alerts.send_alert(title="Goal Expectancy Line Error", text=log_text)
        return False

    if is_home:
        team_id = str(fixture['homeTeam']['_id'])
    else:
        team_id = str(fixture['awayTeam']['_id'])

    odd = dict(
        fixture=fixture,
        player=None,
        teamId=team_id,
        marketId=wsf_market_id,
        bookmaker=sm_odds.bookmaker_name,
        direction="OVER",
        line=line,
        odds=float(sm_odds.odds),
        timestamp=datetime.now()
    )

    logging.info(f"Exact Goals Number - Line {odd['line']}: {odd['odds']}")

    return odd
