import logging
from datetime import datetime

from sportmonks.models import BookmakerOdds

def parse_1x2_market_odds(wsf_market_id: str, fixture: dict, sm_odds: BookmakerOdds, bookmaker_name: str):
    # Handle v3 API format (Home, Draw, Away)
    label = sm_odds['label']
    if label == 'Home':
        line = '1'
    elif label == 'Draw':
        line = 'X'
    elif label == 'Away':
        line = '2'
    else:
        raise ValueError(f"Unknown 1x2 label: {label}")

    if 'bookmaker' in sm_odds and isinstance(sm_odds['bookmaker'], dict):
        bookmaker_name = sm_odds['bookmaker'].get('name', bookmaker_name)

    odd = dict(
        fixture=fixture,
        player=None,
        marketId=wsf_market_id,
        bookmaker=bookmaker_name,
        direction="OVER",
        line=line,
        odds=float(sm_odds['value']),
        timestamp=datetime.now()
    )

    logging.info(f"Market 1x2: Line {odd['line']}: {odd['odds']}")

    return odd
