import logging
from datetime import datetime

from db.db_connection import DBConnection
from sportmonks.market_odds_handlers.commons import retrieve_player, print_parsed_odds_message

# TODO Market Ids are hard coded as they have same sportmonksId of anytime goalscorer
#      but they should eventually be loaded from db as the "anytime"
FIRST_GOALSCORER_MARKET_ID = "604bb21f9fe884c7dc2ab1ad"
LAST_GOALSCORER_MARKET_ID = "604bb21f9fe884c7dc2ab1ae"


def parse_goalscorer_market_odds(dbc: DBConnection, wsf_market_id: str, fixture: dict, sm_odds: dict, bookmaker_name: str):
    sm_player_name = sm_odds['label']

    wsf_player = retrieve_player(dbc, sm_player_name)

    if wsf_player:
        extra = sm_odds.get('extra')
        if extra == 'Anytime':
            odd = dict(
                fixture=fixture,
                player=wsf_player,
                marketId=wsf_market_id,
                bookmaker=bookmaker_name,
                direction="OVER",
                line=0.5,
                odds=float(sm_odds['value']),
                timestamp=datetime.now()
            )

            print_parsed_odds_message(wsf_player, odd, "Anytime Goalscorer")
            return odd
        elif extra == 'First':
            odd = dict(
                fixture=fixture,
                player=wsf_player,
                marketId=FIRST_GOALSCORER_MARKET_ID,
                bookmaker=bookmaker_name,
                direction="OVER",
                line=0.5,
                odds=float(sm_odds['value']),
                timestamp=datetime.now()
            )

            print_parsed_odds_message(wsf_player, odd, "First Goalscorer")
            return odd
        elif extra == 'Last':
            odd = dict(
                fixture=fixture,
                player=wsf_player,
                marketId=LAST_GOALSCORER_MARKET_ID,
                bookmaker=bookmaker_name,
                direction="OVER",
                line=0.5,
                odds=float(sm_odds['value']),
                timestamp=datetime.now()
            )

            print_parsed_odds_message(wsf_player, odd, "Last Goalscorer")
            return odd
        else:
            logging.error(f"Goalscorer 'extra={sm_odds['extra']}' not supported")
