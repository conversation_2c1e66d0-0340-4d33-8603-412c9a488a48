from sportmonks.api.sm_connection import sm_get


def get_fixtures_odds_by_date(date, league_id):
    return sm_get(f"https://soccer.sportmonks.com/api/v2.0/fixtures/date/{date}?leagues={league_id}&include=localTeam,visitorTeam,odds")

def get_fixtures_by_league_and_date_range(league_id, start_date, end_date):
    return sm_get(f"https://soccer.sportmonks.com/api/v2.0/fixtures/between/{start_date}/{end_date}?leagues={league_id}&include=localTeam,visitorTeam,odds")
