from datetime import datetime
from time import sleep

from utils.connection import get


def check_stat(player_data, player_cached_stat_val, new_stat_val, stat_name):
    player = player_data["player"]["data"]
    player_name = player["common_name"]
    player_num = player_data["number"]
    if player_cached_stat_val is None or player_cached_stat_val != new_stat_val:
        print("{}: PLAYER: {} - {} - CHANGE IN STAT {}: {} -> {}".format(datetime.now(), player_name, player_num, stat_name, player_cached_stat_val, new_stat_val))


# fixture_id = "18454443"

token = "q7J4t5OMrfmsuU0WsgMRI9hUJ7o3wFgH72xiMgQFdVeHGdvkzpSlrmQ85zzU"

fixture_id = "18527721"

url = "https://soccer.sportmonks.com/api/v2.0/livescores/now?api_token={}&fixtures={}&include=lineup.player".format(token, fixture_id)

player_stats = {}

while True:

    data = get(url)

    lineup = data['data'][0]["lineup"]["data"]

    for player_data in lineup:
        player_id = player_data["player_id"]

        player_cached_stats = {}
        if player_id in player_stats:
            player_cached_stats = player_stats[player_id]

        stats = player_data["stats"]

        check_stat(player_data, player_cached_stats.get("shots"), stats["shots"]["shots_total"], "shots_total")
        check_stat(player_data, player_cached_stats.get("shots_on_goal"), stats["shots"]["shots_on_goal"], "shots_on_goal")
        check_stat(player_data, player_cached_stats.get("goals"), stats["goals"]["scored"], "goals")
        check_stat(player_data, player_cached_stats.get("fouls"), stats["fouls"]["committed"], "fouls")
        check_stat(player_data, player_cached_stats.get("yellowcards"), stats["cards"]["yellowcards"], "yellowcards")

        player_stats[player_id] = {
            "shots": stats["shots"]["shots_total"],
            "shots_on_goal": stats["shots"]["shots_on_goal"],
            "goals": stats["goals"]["scored"],
            "fouls": stats["fouls"]["committed"],
            "yellowcards": stats["cards"]["yellowcards"]
        }

    sleep(5)



# fallo Bai Jiajun 5 minuti
# You sog 5 minuti
# fallo 13:06:40
# sog Shilin Sun 13:09:05
#

# {
#                     "team_id": 14,
#                     "fixture_id": 17361246,
#                     "player_id": 1878,
#                     "player_name": "Marcus Rashford",
#                     "number": 10,
#                     "position": "A",
#                     "additional_position": null,
#                     "formation_position": 11,
#                     "posx": null,
#                     "posy": null,
#                     "captain": false,
#                     "type": "lineup",
#                     "stats": {
#                         "shots": {
#                             "shots_total": 4,
#                             "shots_on_goal": 3
#                         },
#                         "goals": {
#                             "scored": 1,
#                             "assists": 0,
#                             "conceded": 0,
#                             "owngoals": 0
#                         },
#                         "fouls": {
#                             "drawn": 0,
#                             "committed": 0
#                         },
#                         "cards": {
#                             "yellowcards": 0,
#                             "redcards": 0,
#                             "yellowredcards": 0
#                         },
#                         "passing": {
#                             "total_crosses": 0,
#                             "crosses_accuracy": 0,
#                             "passes": 22,
#                             "accurate_passes": 17,
#                             "passes_accuracy": 77,
#                             "key_passes": 1
#                         },
#                         "dribbles": {
#                             "attempts": 7,
#                             "success": 2,
#                             "dribbled_past": 1
#                         },
#                         "duels": {
#                             "total": 14,
#                             "won": 3
#                         },
#                         "other": {
#                             "aerials_won": 0,
#                             "punches": 0,
#                             "offsides": 0,
#                             "saves": 0,
#                             "inside_box_saves": 0,
#                             "pen_scored": 0,
#                             "pen_missed": 0,
#                             "pen_saved": 0,
#                             "pen_committed": 0,
#                             "pen_won": 0,
#                             "hit_woodwork": 0,
#                             "tackles": 1,
#                             "blocks": 0,
#                             "interceptions": 0,
#                             "clearances": 2,
#                             "dispossesed": 1,
#                             "minutes_played": 90
#                         },
#                         "rating": "7.65"
#                     },
#                     "player": {
#                         "data": {
#                             "player_id": 1878,
#                             "team_id": 14,
#                             "country_id": 462,
#                             "position_id": 4,
#                             "common_name": "M. Rashford",
#                             "display_name": "Marcus Rashford",
#                             "fullname": "Marcus Rashford",
#                             "firstname": "Marcus",
#                             "lastname": "Rashford",
#                             "nationality": "England",
#                             "birthdate": "31/10/1997",
#                             "birthcountry": "England",
#                             "birthplace": "Manchester",
#                             "height": "180 cm",
#                             "weight": "70 kg",
#                             "image_path": "https://cdn.sportmonks.com/images/soccer/players/22/1878.png"
#                         }
#                     }
#                 },
