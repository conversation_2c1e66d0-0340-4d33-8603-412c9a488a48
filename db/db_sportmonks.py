class SportmonksDBConnection:

    def __init__(self, sportmonks_db, main_db):
        self.sportmonks_db = sportmonks_db
        self.main_db = main_db

    def save_sportmonks_team(self, team):
        res = self.sportmonks_db.teams.replace_one({
            "id": team["id"]
        }, team, upsert=True)
        return res

    def save_sportmonks_league(self, league):
        res = self.sportmonks_db.leagues.replace_one({
            "id": league["id"]
        }, league, upsert=True)
        return res

    def get_mapped_player(self, player_label):
        return self.sportmonks_db.mapped_players.find_one({'fullName': player_label})
