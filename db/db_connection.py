from pymongo import <PERSON>go<PERSON><PERSON>

from db.db_competitions import CompetitionsDBConnection
from db.db_main import MainDBConnection
from db.db_market_odds import MarketOddsDBConnection
from db.db_sportmonks import SportmonksDBConnection

MAIN_DB_NAME = "main"
SOCCER_DB_NAME = "all_leagues"
MARKET_ODDS_DB_NAME = "market_odds"
SPORTMONKS_DB_NAME = "sportmonks"


class DBConnection:

    def __init__(self, source_client: MongoClient, market_odds_client: MongoClient):
        self.source_client = source_client
        self.market_odds_client = market_odds_client

        main_db_client = self.source_client[MAIN_DB_NAME]
        self.main = MainDBConnection(main_db_client)
        self.market_odds = MarketOddsDBConnection(market_odds_client[MARKET_ODDS_DB_NAME])
        self.sportmonks = SportmonksDBConnection(self.source_client[SPORTMONKS_DB_NAME], main_db_client)
        self.competitions = CompetitionsDBConnection(self.source_client[SOCCER_DB_NAME])

    def get_db(self, db_name):
        return self.source_client[db_name]
