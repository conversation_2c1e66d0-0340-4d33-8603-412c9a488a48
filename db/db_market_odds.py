from bson import ObjectId
from pymongo.database import Database


class MarketOddsDBConnection:

    def __init__(self, market_odds_db: Database):
        self.market_odds_db = market_odds_db

    def do_odds_exist(self, fixture_id, market_id) -> bool:
        nr_odds = self.market_odds_db["sm_market_odds"].count_documents({"fixture._id": ObjectId(fixture_id),
                                                                         "marketId": market_id})
        return nr_odds > 0

    def save_sportmonks_market_odds(self, odds):
        res = self.market_odds_db["sm_market_odds"].replace_one({
            "fixture._id": ObjectId(odds["fixture"]["_id"]),
            "line": odds["line"],
            "direction": odds["direction"],
            "player": odds["player"],
            "marketId": odds["marketId"]
        }, odds, upsert=True)
        return res

    def delete_sm_market_odds(self, fixture_id: str, market_id: str):
        res = self.market_odds_db["sm_market_odds"].delete_many({
            "fixture._id": ObjectId(fixture_id),
            "marketId": market_id,
        })
        return res

    def bulk_insert_market_odds(self, odds):
        res = self.market_odds_db["sm_market_odds"].insert_many(odds)
        return res
