import logging
from datetime import datetime, timedelta
from typing import List

from bson.objectid import ObjectId


class CompetitionsDBConnection:
    def __init__(self, soccer_db):
        self.soccer_db = soccer_db

    def get_wsf_player_by_master_id(self, player_master_id):
        return self.soccer_db.master_players.find_one({"playerMasterId": player_master_id})

    def get_active_competitions(self):
        return self.soccer_db.competitions.find({"active": True, "externalIds.sportmonksIds": {"$exists": True}})

    def get_competition_by_id(self, competition_id):
        return self.soccer_db.competitions.find({"_id": ObjectId(competition_id)})

    def get_tournament_for_years(self, competition_id, years):
        return self.soccer_db.tournaments.find_one({
            "competition._id": ObjectId(competition_id),
            "year": years})

    def get_current_tournament(self, competition_id):
        return self.soccer_db.tournaments.find_one({
            "competition._id": ObjectId(competition_id),
            "active": True, 'current': True})

    def get_competition_fixtures_by_teams(self, tournament_id, home_team_id, away_team_id):
        args = {
            "tournament._id": ObjectId(tournament_id),
            "homeTeam._id": ObjectId(home_team_id),
            "awayTeam._id": ObjectId(away_team_id)
        }
        fixture = self.soccer_db.fixtures.find_one(args)
        if fixture is None:
            logging.info(f"Fixture not found ({args})")
        return fixture

    def get_competition_fixtures_by_teams_and_date(self, tournament_id, home_team_id, away_team_id, date) -> dict:
        args = {
            "tournament._id": ObjectId(tournament_id),
            "homeTeam._id": ObjectId(home_team_id),
            "awayTeam._id": ObjectId(away_team_id),
            "date": {"$gte": date + timedelta(days=-1), "$lte": date + timedelta(days=1)},
            "wasReplaced": {"$ne": True}
        }
        fixtures = list(self.soccer_db.fixtures.find(args))
        if len(fixtures) == 1:
            return fixtures[0]
        if len(fixtures) == 0:
            raise Exception(f"Fixture not found ({args})")
        elif len(fixtures) > 1:
            raise Exception(f"Multiple fixtures found ({args})")

    def get_fixture_by_sportmonks_id(self, sm_fixture_id):
        args = {
            "sportmonksFixtureId": sm_fixture_id,
            "wasReplaced": {"$ne": True}
        }
        fixture = self.soccer_db.fixtures.find_one(args)
        if fixture is None:
            logging.info(f"Fixture not found ({args})")
        return fixture

    def get_team_by_sportmonks_id(self, sm_team_id):
        return self.soccer_db.master_teams.find_one({"sportmonksId": str(sm_team_id)})

    def update_fixture_sportmonks_id(self, fixture, sm_fixture_id):
        res = self.soccer_db.fixtures.update_one({"_id": fixture["_id"]},
                                                 {"$set": {"sportmonksFixtureId": sm_fixture_id}})
        return res

    def get_fixtures_by_competition_next_days(self, competition_id: str, days: int) -> List[dict]:
        current_date = datetime.now()
        end_date = current_date + timedelta(days=days)

        return list(self.soccer_db.fixtures.find({
            "tournament.competition._id": ObjectId(competition_id),
            "date": {
                "$gte": current_date,
                "$lte": end_date
            },
            "wasReplaced": {"$ne": True},
            "status": "FIXTURE"
        }).sort("date", 1))
