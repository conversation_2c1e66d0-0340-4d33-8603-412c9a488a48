import os
from requests import get, post

MOI_ENTITY_MAPPER_API = os.getenv("MOI_ENTITY_MAPPER_API", None)


def get_mapped_player_name(player_label):
    if MOI_ENTITY_MAPPER_API is None:
        raise Exception("env var MOI_ENTITY_MAPPER_API cannot be null")

    body = {
        "label": player_label,
        "provider": "SPORTMONKS_BET365"
    }
    res = get(f"{MOI_ENTITY_MAPPER_API}/v1/labels", json=body)
    if res.status_code == 200:
        return res.json()
    return None


def get_labels_count():
    if MOI_ENTITY_MAPPER_API is None:
        raise Exception("env var MOI_ENTITY_MAPPER_API cannot be null")
    res = get(f"{MOI_ENTITY_MAPPER_API}/v1/labels/count")
    if res.status_code == 200:
        return res.json()
    return None


def create_unmapped_team(team_id, team_name):
    if MOI_ENTITY_MAPPER_API is None:
        raise Exception("env var MOI_ENTITY_MAPPER_API cannot be null")
    body = {
        "name": team_name,
        "provider_team_id": str(team_id),
        "provider": "SPORTMONKS"
    }
    res = post(f"{MOI_ENTITY_MAPPER_API}/v1/unmapped-teams/find-or-create", json=body)
    if res.status_code == 201:
        return True
    elif res.status_code == 404:
        return False
    raise Exception("Http Code {} not supported".format(res.status_code))
