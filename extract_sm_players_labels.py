import db.db_connection
import mapper_api.teams

dbc = db.db_connection.DBConnection()

sm_db = dbc.get_sportmonks_db()
sm_players = sm_db.players.find({})

# This script extracts labels from sportmonks players and save them into the following collections:
# - sm_player_labels
# - sm_player_labels_no_team (players without a known team_id)

for sm_player in sm_players:
    sm_team_id = sm_player["team_id"]
    team = dbc.competitions.get_team_by_sportmonks_id(sm_team_id)
    if team:

        player_label = {
            "fullname": sm_player["fullname"],
            "common_name": sm_player["common_name"],
            "display_name": sm_player["display_name"],
            "team_id": team['_id'],
            "team_name": team['name']
        }
        sm_db.sm_player_labels.replace_one({"fullname": sm_player["fullname"]}, player_label, upsert=True)
    else:
        player_label = {
            "fullname": sm_player["fullname"],
            "common_name": sm_player["common_name"],
            "display_name": sm_player["display_name"]
        }
        sm_db.sm_player_labels_no_team.replace_one({"fullname": sm_player["fullname"]}, player_label, upsert=True)

