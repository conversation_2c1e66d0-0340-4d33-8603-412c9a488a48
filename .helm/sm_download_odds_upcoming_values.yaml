app:
  vars:
    MOI_SCRIPT_NAME: "SPORTMONKS_DOWNLOAD_ODDS"
    MOI_ODDS_SCRIPT_TYPE: "upcoming"
    MOI_ENTITY_MAPPER_API: "http://entity-mapper-prod"
    TEST_MODE: "false"

cronjob:
  image:
    repository: 268764888866.dkr.ecr.eu-west-1.amazonaws.com/services-release/market-odds-ingestor
  name: sportmonks-download-odds-upcoming
  schedule: "3 3 * * *" # every day at 3.03am utc

service:
  env: prod
  type: NodePort # change to ClusterIP
  port: 5000
  roles:
    - public