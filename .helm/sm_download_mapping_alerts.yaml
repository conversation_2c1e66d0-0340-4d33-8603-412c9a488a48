app:
  vars:
    MOI_SCRIPT_NAME: "PLAYER_MAPPING_ALERT"
    MOI_ENTITY_MAPPER_API: "http://entity-mapper-prod"
    PLAYER_MAPPING_SLACK_WEBHOOK: "*********************************************************************************"
    TEST_MODE: "false"

cronjob:
  image:
    repository: 268764888866.dkr.ecr.eu-west-1.amazonaws.com/services-release/market-odds-ingestor
  name: players-mapping-alerts
  schedule: "10 10 */1 * *" # every day at 11.10am

service:
  env: prod
  type: NodePort # change to ClusterIP
  port: 5000
  roles:
    - public