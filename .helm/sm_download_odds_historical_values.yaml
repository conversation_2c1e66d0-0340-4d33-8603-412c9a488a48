app:
  vars:
    MOI_SCRIPT_NAME: "SPORTMONKS_DOWNLOAD_ODDS"
    MOI_ODDS_SCRIPT_TYPE: "historical"
    MOI_ENTITY_MAPPER_API: "http://entity-mapper-prod"
    MOI_MARKET_IDS_TO_DOWNLOAD: "604bb21f9fe884c7dc2ab1ac,604bb21f9fe884c7dc2ab1a4,604bb21f9fe884c7dc2ab1a3,604bb21f9fe884c7dc2ab1a0,604bb21f9fe884c7dc2ab1a5,604bb21f9fe884c7dc2ab1a1,604bb21f9fe884c7dc2ab1aa" #1x2 only
    TEST_MODE: "false"

cronjob:
  image:
    repository: 268764888866.dkr.ecr.eu-west-1.amazonaws.com/services-release/market-odds-ingestor
  name: sportmonks-download-odds-historical
  schedule: "30 9 */1 * *"

service:
  env: prod
  type: NodePort # change to ClusterIP
  port: 5000
  roles:
    - public