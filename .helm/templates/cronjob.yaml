apiVersion: batch/v1
kind: CronJob
metadata:
  name: {{ .Values.cronjob.name }}
spec:
  schedule: {{ .Values.cronjob.schedule | quote}}
  timeZone: "Etc/UTC"
  successfulJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          restartPolicy: Never
          containers:
            - name: {{ .Values.cronjob.name }}
              image: "{{ .Values.cronjob.image.repository }}:{{ .Chart.AppVersion }}"
              ports:
                - name: http
                  containerPort: {{.Values.service.port}}
                  protocol: TCP
              env:
                - name: MOI_SPORTMONKS_API_TOKEN
                  valueFrom:
                    secretKeyRef:
                      name: market-odds-secret-{{ .Values.service.env }}
                      key: MOI_SPORTMONKS_API_TOKEN
                - name: MOI_DB_URL
                  valueFrom:
                    secretKeyRef:
                      name: market-odds-secret-{{ .Values.service.env }}
                      key: DB_URL
                - name: MOI_DB_URL_MARKET_ODDS
                  valueFrom:
                    secretKeyRef:
                      name: market-odds-secret-{{ .Values.service.env }}
                      key: DB_URL
                - name: MOI_SCRIPT_NAME
                  value: {{ .Values.app.vars.MOI_SCRIPT_NAME }}
                - name: MOI_ENTITY_MAPPER_API
                  value: {{ .Values.app.vars.MOI_ENTITY_MAPPER_API }}
                - name: MOI_ODDS_MARKET_ID
                  value: {{ .Values.app.vars.MOI_ODDS_MARKET_ID }}
                - name: MOI_ODDS_SCRIPT_TYPE
                  value: {{ .Values.app.vars.MOI_ODDS_SCRIPT_TYPE }}
                - name: PLAYER_MAPPING_SLACK_WEBHOOK
                  value: {{ .Values.app.vars.PLAYER_MAPPING_SLACK_WEBHOOK}}
                - name: MOI_MARKET_IDS_TO_DOWNLOAD
                  value: {{ .Values.app.vars.MOI_MARKET_IDS_TO_DOWNLOAD}}
                - name: TEST_MODE
                  value: {{ .Values.app.vars.TEST_MODE | quote }}