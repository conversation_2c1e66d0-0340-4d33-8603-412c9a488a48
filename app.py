import logging
import os

from pymongo import MongoClient
from wsfcommonlibs.db.connection import DBConnection as DBConnLibs

from db.db_connection import DBConnection
from sportmonks.download_market_odds import DownloadMarketOdds
from sportmonks.slack_alerts.market_odds_alerts import MarketOddsAlerts

logging.basicConfig(level="INFO", format='%(asctime)s - %(levelname)s - %(message)s')
logging.getLogger().setLevel(logging.INFO)

if __name__ == '__main__':

    script_mode = os.getenv("MOI_SCRIPT_NAME", None)
    test_mode = os.getenv("TEST_MODE", 'false')
    db_url = os.getenv("MOI_DB_URL", None)
    db_url_market_odds = os.getenv("MOI_DB_URL_MARKET_ODDS", None)

    if script_mode is None:
        raise Exception(f"Env var MOI_SCRIPT_NAME not defined. Please fix it and try again")

    if db_url is None:
        raise Exception("env var MOI_DB_URL cannot be null")

    source_client = MongoClient(db_url)
    db_url_market_odds = db_url_market_odds if db_url_market_odds else db_url

    logging.info(f"Script mode: {script_mode}")
    logging.info(f"Source DB: {db_url}")
    logging.info(f"market_odds DB: {db_url_market_odds}")

    if db_url != db_url_market_odds:
        market_odds_client = MongoClient(db_url_market_odds)
    else:
        market_odds_client = source_client

    db_connection = DBConnection(source_client, market_odds_client)
    db_connection_libs = DBConnLibs(db_url)
    is_test_mode = True if test_mode == 'true' else False
    market_odds_alerts = MarketOddsAlerts(db_connection, is_test_mode)
    download_market_odds = DownloadMarketOdds(market_odds_alerts, db_connection, db_connection_libs)

    if script_mode.upper() == 'SPORTMONKS_DOWNLOAD_ODDS':
        download_market_odds.download_sportmonks_market_odds()
    elif script_mode.upper() == 'SPORTMONKS_DOWNLOAD_LEAGUES':
        from sportmonks.download_leagues import download_sportmonks_leagues

        download_sportmonks_leagues()
    elif script_mode.upper() == 'PLAYER_MAPPING_ALERT':
        from sportmonks.mapping_alert import mapping_alert

        mapping_alert()
    else:
        raise Exception(f"script {script_mode} not supported")
